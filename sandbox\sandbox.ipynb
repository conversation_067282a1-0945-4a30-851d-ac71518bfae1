import pandas as pd
import numpy as np
import scipy as sp
import matplotlib.pyplot as plt
import seaborn as sns

df = pd.read_csv("data\ข้อมูลสำหรับสร้างตัวแบบ.csv")
print(df)

# Calculate R-squared
from scipy.stats import pearsonr
from sklearn.metrics import r2_score
from sklearn.linear_model import LinearRegression

# Calculate correlation coefficient and R-squared
correlation, p_value = pearsonr(df['n'], df['AP (hPa)'])
r_squared = correlation ** 2

# Alternative method using linear regression
X = df[['n']]
y = df['AP (hPa)']
model = LinearRegression().fit(X, y)
y_pred = model.predict(X)
r2_sklearn = r2_score(y, y_pred)

# plotting
sns.set(style="darkgrid")
sns.set_context("notebook")

fig, ax = plt.subplots(figsize=(10, 6))
sns.scatterplot(x="n", y="AP (hPa)", data=df, ax=ax)

# Add trend line
sns.regplot(x="n", y="AP (hPa)", data=df, ax=ax, scatter=False, color='red')

# Add R-squared value as text on the plot
ax.text(0.05, 0.95, f'R² = {r_squared:.4f}', transform=ax.transAxes, 
        fontsize=14, verticalalignment='top', 
        bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

plt.title(f'Scatter Plot with R² = {r_squared:.4f}')
plt.show()

print(f"Correlation coefficient: {correlation:.4f}")
print(f"R-squared: {r_squared:.4f}")
print(f"R-squared (sklearn): {r2_sklearn:.4f}")
print(f"P-value: {p_value:.4f}")