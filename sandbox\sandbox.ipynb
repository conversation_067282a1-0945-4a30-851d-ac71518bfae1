import pandas as pd
import numpy as np
import scipy as sp
import matplotlib.pyplot as plt
import seaborn as sns

df = pd.read_csv("data\ข้อมูลสำหรับสร้างตัวแบบ.csv")
print(df)

# plotting
sns.set(style="darkgrid")
sns.set_context("notebook")

# Linear regression plot between DTR and MOI
fig, ax = plt.subplots(figsize=(10, 6))
sns.regplot(x="DTR", y="MOI (%)", data=df, ax=ax)
plt.title("Linear Relationship between DTR and MOI")
plt.show()

# Alternative using lmplot (figure-level function)
sns.lmplot(x="DTR", y="MOI (%)", data=df, height=6, aspect=1.5)
plt.title("Linear Regression of MOI vs DTR")
plt.tight_layout()
plt.show()