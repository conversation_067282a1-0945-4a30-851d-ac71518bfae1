

---

# โจทย์แบบทดสอบสร้างแบบจำลองความชื้นในอากาศ

## ระยะเวลาทำแบบทดสอบ

**เริ่ม:** วันที่ 26 พฤษภาคม เวลา 12.00 น.
**สิ้นสุด:** วันที่ 28 พฤษภาคม เวลา 12.00 น.
**หมายเหตุ:** ให้ทำแบบทดสอบในช่วงเวลาที่ทีมว่าง

---

## โจทย์

### 1. สร้างแบบจำลองความชื้นในอากาศ (MOI, %)

โดยใช้ตัวแปรต้นดังนี้:

* อุณหภูมิ (T) - หน่วย: °C
* ความกดอากาศเฉลี่ย (AP) - หน่วย: hPa
* ช่วงอุณหภูมิรายวัน (DTR)
* ทิศทางลม (WD) *(เป็นตัวแปรเชิงคุณภาพ)*

> **ข้อมูลใช้เฉพาะพื้นที่จังหวัดขอนแก่น ช่วงเดือนมกราคมถึงเมษายน**

---

## 2. การประเมินแบบจำลอง

### 2.1 วัดประสิทธิภาพของแบบจำลองด้วย Adjusted R²

พร้อมอธิบายผลลัพธ์ที่ได้อย่างชัดเจน

### 2.2 อธิบายเหตุผลที่เลือกใช้โมเดลดังกล่าว

### 2.3 ใช้โมเดลที่สร้างขึ้นในการพยากรณ์ (Predict) และส่งผลลัพธ์มาที่:

**[<EMAIL>](mailto:<EMAIL>)**
**สิ่งที่ต้องส่ง:**

* ผลลัพธ์การพยากรณ์
* ไฟล์ `.ipynb` (Jupyter Notebook) ที่แสดงขั้นตอนการ Train และ Predict อย่างครบถ้วน
* ส่งภายในวันที่ **28 พฤษภาคม 2568 ก่อนเวลา 12.00 น.**

### 2.4 บันทึกผลลัพธ์การ Predict เป็นไฟล์ `predicted_y.csv`

**หมายเหตุ:**

* ต้องจัดรูปแบบให้เหมือนกับตัวอย่างที่ให้มาใน `example_y.csv`

---

## การให้คะแนน

* **คะแนนจากขั้นตอนการสร้างแบบจำลอง: 60 คะแนน**
* **คะแนนจากผล MAE (Mean Absolute Error): 40 คะแนน**

  * เรียงลำดับคะแนนจากค่า MAE น้อยไปมาก ตามลำดับดังนี้:
    40, 35, 30, 25, 20, ...

---

## หมายเหตุเพิ่มเติม

1. ตัวแปร **WD (ทิศทางลม)** ให้แบ่งออกเป็น 2 โซน:

   * โซนทิศเหนือ → **0**
   * โซนทิศใต้ → **1**

---

## ตัวแปรที่ใช้ในแบบจำลอง

| ตัวแปร | รายละเอียด                    | หน่วย |
| ------ | ----------------------------- | ----- |
| T      | อุณหภูมิ                      | °C    |
| AP     | ความกดอากาศเฉลี่ย             | hPa   |
| DTR    | ช่วงอุณหภูมิรายวัน            | -     |
| WD     | ทิศทางลม (0 = เหนือ, 1 = ใต้) | -     |
| MOI    | ความชื้นในอากาศ               | %     |